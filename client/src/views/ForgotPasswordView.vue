<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          忘記密碼
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          請輸入您的郵箱地址，我們將發送驗證碼到您的郵箱
        </p>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <ExclamationTriangleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              {{ error }}
            </h3>
          </div>
        </div>
      </div>

      <!-- 成功提示 -->
      <div v-if="success" class="rounded-md bg-green-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <CheckCircleIcon class="h-5 w-5 text-green-400" aria-hidden="true" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">
              {{ success }}
            </h3>
          </div>
        </div>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleSendCode">
        <div>
          <label for="email" class="sr-only">郵箱地址</label>
          <input
            id="email"
            v-model="form.email"
            name="email"
            type="email"
            autocomplete="email"
            required
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
            placeholder="請輸入您的郵箱地址"
          />
        </div>

        <div>
          <button
            type="submit"
            :disabled="isLoading || !isValidEmail(form.email)"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
            {{ isLoading ? '發送中...' : '發送驗證碼' }}
          </button>
        </div>

        <div class="text-center">
          <router-link
            to="/login"
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            返回登錄
          </router-link>
        </div>
      </form>

      <!-- 验证码发送成功后显示下一步 -->
      <div v-if="codeSent" class="mt-6 text-center">
        <p class="text-sm text-gray-600 mb-4">
          驗證碼已發送到您的郵箱，請查收並點擊下方按鈕繼續
        </p>
        <router-link
          :to="{ name: 'reset-password', query: { email: form.email } }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          繼續重置密碼
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/vue/24/outline'
import { sendVerificationCode } from '@/api/emailVerification'
import { isValidEmail } from '@/utils/helpers'

// 表单数据
const form = ref({
  email: ''
})

// 状态
const isLoading = ref(false)
const error = ref('')
const success = ref('')
const codeSent = ref(false)

// 发送验证码
const handleSendCode = async () => {
  if (!form.value.email || !isValidEmail(form.value.email)) {
    error.value = '請輸入有效的郵箱地址'
    return
  }

  try {
    isLoading.value = true
    error.value = ''
    success.value = ''

    await sendVerificationCode({
      email: form.value.email,
      type: 'reset_password'
    })

    success.value = '驗證碼已發送到您的郵箱，請查收'
    codeSent.value = true

  } catch (err: any) {
    error.value = err.response?.data?.message || '發送驗證碼失敗，請稍後重試'
  } finally {
    isLoading.value = false
  }
}
</script>
