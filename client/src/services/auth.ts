import { apiRequest } from '@/utils/api'
import type {
  User,
  LoginRequest,
  RegisterRequest,
  ResetPasswordRequest,
  AuthResponse,
  ApiResponse
} from '@/types'

/**
 * 认证相关API服务
 */
export const authService = {
  /**
   * 用户注册
   */
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    return await apiRequest.post('/auth/register', data) as AuthResponse
  },

  /**
   * 用户登录
   */
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    return await apiRequest.post('/auth/login', data) as AuthResponse
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    return await apiRequest.get<User>('/auth/me')
  },

  /**
   * 修改密码
   */
  changePassword: async (data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> => {
    return await apiRequest.put('/auth/change-password', data)
  },

  /**
   * 重置密码（忘记密码）
   */
  resetPassword: async (data: ResetPasswordRequest): Promise<ApiResponse> => {
    return await apiRequest.post('/auth/reset-password', data)
  },

  /**
   * 刷新token
   */
  refreshToken: async (): Promise<AuthResponse> => {
    return await apiRequest.post('/auth/refresh') as AuthResponse
  },

  /**
   * 登出
   */
  logout: async (): Promise<ApiResponse> => {
    return await apiRequest.post('/auth/logout')
  },
}
